// React核心库和组件导入
import React, {Component} from 'react';
import {
    StyleSheet,      // 样式表
    View,           // 视图容器
    TouchableOpacity, // 可触摸的透明度组件
    Image,          // 图片组件
    Dimensions      // 设备尺寸获取
} from 'react-native';
import PropType from 'prop-types';  // 属性类型检查
import {inject, observer} from 'mobx-react/native';  // MobX状态管理装饰器
import {toJS} from 'mobx';  // MobX工具函数
import {ReactNavComponent, Widget} from 'rn-yunxi';  // 自定义导航组件和UI组件
import AppWidget from '../../app-widget';  // 应用级UI组件

// 获取设备屏幕尺寸
const {width, height} = Dimensions.get('window');
// 解构获取应用级组件
const {Header,CommonSearchBar} = AppWidget;
// 解构获取通用UI组件
const {CommonFlatList, Text} = Widget;

/**
 * 收货地址列表页面组件
 * 功能：
 * 1. 显示用户的收货地址列表
 * 2. 支持搜索地址功能
 * 3. 支持下拉刷新和上拉加载更多
 * 4. 支持地址选择回调（用于其他页面选择地址）
 */
@inject(stores => ({
    address: stores.address,  // 注入地址相关的MobX store
}))
@observer  // 观察者模式，自动响应store数据变化
export default class AddressList extends ReactNavComponent {
    // 组件属性类型声明（当前为空，可根据需要添加）
    static propTypes = {};

    // 组件默认属性（当前为空，可根据需要添加）
    static defaultProps = {};

    /**
     * 构造函数
     * @param {Object} props - 组件属性
     */
    constructor(props) {
        super(props);
        // 组件内部状态初始化（当前为空）
        this.state = {
        };
    }

    /**
     * 组件即将挂载时的生命周期方法
     * 在组件渲染前获取地址列表数据
     */
    componentWillMount() {
        // 获取地址列表，参数false表示不是加载更多操作
        this.props.address.obtainAddressList(false).then().catch();
    }

    /**
     * 组件挂载完成后的生命周期方法
     * 当前为空实现，可在此处添加需要在组件挂载后执行的逻辑
     */
    componentDidMount() {
    }

    /**
     * 组件即将卸载时的生命周期方法
     * 当前为空实现，可在此处添加清理逻辑
     */
    componentWillUnmount() {
    }

    /**
     * 渲染地址列表项
     * @param {Object} param - 包含item和index的对象
     * @param {Object} param.item - 地址数据项
     * @param {number} param.index - 列表项索引
     * @returns {JSX.Element} 地址列表项组件
     */
    renderItem=({item, index})=> {
        // 获取导航参数，用于判断是否有回调函数
        let {params}=this.getNavState();
        return (
            <TouchableOpacity
                style={{backgroundColor: 'white',padding:Constant.sizeMarginDefault}}
                onPress={() => {
                    // 如果存在回调函数，说明是从其他页面跳转过来选择地址的
                    if(params&&params.callBack) {
                        // 执行回调函数，将选中的地址传回上一页面
                        params.callBack(item);
                        // 返回上一页面
                        this.goBack();
                    }
                }}
            >
                {/* 地址项主容器 */}
                <View style={{flexDirection: 'row', flex: 1}}>
                    {/* 地址信息容器 */}
                    <View style={{flex: 1, alignSelf: 'center',}}>
                        {/* 联系人和手机号行 */}
                        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                            {/* 联系人姓名和手机号 */}
                            <Text style={[addressStyles.nameText, {fontSize: Constant.fontSizeBig}]}>
                                {item.contactLevel} {item.contactMobile}
                            </Text>
                            {/* 地址标签（如：公司、家等） */}
                            <Text style={{
                                height: Constant.scale(18),
                                textAlign: 'center',
                                paddingTop: Constant.scale(1),
                                paddingBottom: Constant.scale(1),
                                paddingLeft: Constant.scale(1.5),
                                paddingRight: Constant.scale(1.5),
                                backgroundColor: Constant.colorPrimary,
                                color: 'white',
                                fontSize: Constant.fontSizeSmall,
                                borderRadius: Constant.scale(3),
                            }}>{item.abbreviation}</Text>
                        </View>

                        {/* 详细地址行 */}
                        <View style={{flexDirection: 'row', marginTop: 5}}>
                            <Text
                                numberOfLines={2}  // 最多显示2行
                                style={[addressStyles.addressText, {fontSize: Constant.fontSizeSmall}]}
                            >{item.address}</Text>
                        </View>
                    </View>
                </View>
            </TouchableOpacity>
        )
    }

    /**
     * FlatList的key提取器
     * 为每个列表项生成唯一的key值，用于React的虚拟DOM优化
     * @param {Object} item - 地址数据项（未使用，但保留参数以符合FlatList接口）
     * @param {number} index - 列表项索引
     * @returns {string} 唯一的key值
     */
    keyExtractor = (item, index) => {
        return 'addressList' + index;
    };

    /**
     * 渲染列表项之间的分隔线
     * @returns {JSX.Element} 分隔线组件
     */
    renderSeparator = () => {
        return (
            <View style={{height: Constant.sizeMarginDefault, backgroundColor: Constant.colorBackgroundDefault}}/>
        );
    };

    /**
     * 渲染主界面
     * @returns {JSX.Element} 地址列表页面组件
     */
    render() {
        // 从address store中获取列表相关参数
        let {listParams} = this.props.address;
        // 搜索框背景色（白色半透明）
        let searchAlpha = "rgba(255,255,255,1 )";

        return (
            <Header style={styles.container} title={'地址列表'}>
                {/* 搜索栏容器 */}
                <View style={{ flexDirection: "row", alignItems: "center", height:Constant.scale(40) }}>
                    <CommonSearchBar
                        style={{
                            flex: 1,
                            margin: 0,
                            marginLeft: Constant.sizeMarginDefault,
                            marginRight: Constant.sizeMarginDefault / 2
                        }}
                        searchBarTxtInputStyle={{ backgroundColor: searchAlpha }}  // 搜索框背景样式
                        cancelBtnStyle={{color:Constant.colorPrimary}}  // 取消按钮样式
                        placeholder={'搜索地址'}  // 占位符文本
                        placeholderTextColor={Constant.colorTxtAlert}  // 占位符文本颜色
                        onSearchBtnClick = {()=>this.props.address.obtainAddressList(false)}  // 搜索按钮点击事件
                        onChange={(text)=>this.props.address.setSearchAddress(text)}  // 搜索文本变化事件
                        text={this.props.address.searchAddress}  // 当前搜索文本
                        clear={()=>this.props.address.clearSearchAddress()}  // 清空搜索文本
                        onFocus={()=>this.props.address.setSearchAddress(this.props.address.searchAddress)}  // 获取焦点事件
                        isOnFocus={false}  // 是否处于焦点状态
                    />
                </View>

                {/* 地址列表 */}
                <CommonFlatList
                    ItemSeparatorComponent={this.renderSeparator}  // 分隔线组件
                    onRefresh={() => {
                        // 下拉刷新事件：重新获取地址列表
                        this.props.address.obtainAddressList(false).then().catch();
                    }}
                    onLoadMore={(info) => {
                        // 上拉加载更多事件：当距离底部大于0时加载更多数据
                        if(info.distanceFromEnd>0) {
                            this.props.address.obtainAddressList(true).then().catch();
                        }
                    }}
                    initialNumToRender={6}  // 初始渲染的列表项数量
                    keyExtractor={this.keyExtractor}  // key提取器
                    renderItem={this.renderItem}  // 列表项渲染函数
                    data={listParams.dataArray}  // 列表数据源
                    enableLoadMore={listParams.enableLoadMore}  // 是否启用加载更多
                    enableRefresh={listParams.enableRefresh}  // 是否启用下拉刷新
                    listState={listParams.listState}  // 列表状态（加载中、成功、失败等）
                    extraData={this.props}  // 额外数据，用于触发重新渲染
                    style={{flex: 1}}  // 列表样式
                >
                    {/* 空数据时显示的占位内容 */}
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>
                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </Header>
        );
    }
};

/**
 * 主容器样式
 */
const styles = StyleSheet.create({
    container: {
        flex: 1,  // 占满整个屏幕
    }
});

/**
 * 地址列表项相关样式
 */
const addressStyles = StyleSheet.create({
    // 地址项背景样式（当前未使用，但保留以备后用）
    background: {
        backgroundColor: 'white',  // 白色背景
        width: width,  // 屏幕宽度
        minHeight: Constant.scale(100),  // 最小高度
        paddingLeft: Constant.sizeMarginDefault,  // 左内边距
        marginBottom: Constant.sizeMarginDefault,  // 底部外边距
    },
    // 联系人姓名和手机号文本样式
    nameText: {
        color: Constant.colorTxtContent,  // 内容文本颜色
        fontSize: Constant.fontSizeBig,   // 大字体
    },
    // 详细地址文本样式
    addressText: {
        color: Constant.colorTxtContent,   // 内容文本颜色
        fontSize: Constant.fontSizeNormal, // 普通字体大小
    }
});